import { addDays, differenceInYears, format, parse } from 'date-fns';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { deleteCashPoolBatch, getCashPool, getCashPoolBatches } from '~/api';
import { DeleteModal } from '~/components/Modals';
import { UserInfoContext } from '~/context/user';
import { batchStatus, DATE_FNS_FORMATS, NORDIC } from '~/enums';
import { cashPoolSelector, setCashPool, updateCashPoolTrigger } from '~/reducers/cashPool.slice';
import { cashPoolDataSelector, updateDateRange, updateField } from '~/reducers/cashPoolData.slice';
import { updateDateRange as updateStructuralPositionsDateRange } from '~/reducers/cashPoolStructuralPositions.slice';
import { LoadingSpinner, Pagination, Table } from '~/ui';
import { showToast } from '~/ui/components/Toast';
import { errorHandler } from '~/utils/errors';

import BatchesCreateButton from './BatchesCreateButton';
import { columns, getCashPoolBatchesData, renderTableActionColumn } from './BatchesTable.utils';

const BatchesTable = () => {
  const history = useHistory();
  const dispatch = useDispatch();
  const { allParticipants } = useSelector(cashPoolDataSelector);
  const { topCurrencyAccounts, type } = useSelector(cashPoolSelector);
  const { cashPoolId } = useParams();
  const { userInfo } = useContext(UserInfoContext);
  const [paginationOffset, setPaginationOffset] = useState(0);
  const [batches, setBatches] = useState();
  const [isDeleteModalShowing, setIsDeleteModalShowing] = useState(false); // false or set to table row values of batch
  const limit = 10;
  const isNordic = type === NORDIC;

  const onPageChange = ({ selected }) => setPaginationOffset(selected);

  /**
   * Sets up the initial Data View Graph by showing the data of the last pooled batch
   *
   * Dash is replaced with forward slash to have same result when setting initial filter
   * and when clicking on a row in the batches table. They have a difference of two hours
   * when cast to a date. Date with forward slash is midnight while date with dash is 2 a.m.
   */
  const setBatchFilter = useCallback(
    (batch, areDatesFormatted = false) => {
      if (!batch) return;

      let { startDate, endDate } = batch;

      // If dates were taken from batches table they were formated to userInfo.dateFormat, so they need to be formatted to ISO format first
      if (areDatesFormatted) {
        startDate = format(parse(startDate, DATE_FNS_FORMATS[userInfo.dateFormat], new Date()), DATE_FNS_FORMATS.ISO);
        endDate = format(parse(endDate, DATE_FNS_FORMATS[userInfo.dateFormat], new Date()), DATE_FNS_FORMATS.ISO);
      }
      if (differenceInYears(new Date(endDate), new Date(startDate)) >= 1) {
        endDate = format(addDays(new Date(startDate), 30), DATE_FNS_FORMATS.ISO);
      }
      const companyIds = allParticipants.map(({ companyId }) => companyId);

      isNordic && dispatch(updateField({ chosenTopCurrencyAccountId: topCurrencyAccounts[0]?.id }));
      dispatch(updateDateRange({ startDate, endDate }));
      dispatch(updateField({ chosenCompanyIds: companyIds }));
    },
    [allParticipants, dispatch, topCurrencyAccounts, isNordic, userInfo.dateFormat]
  );

  useEffect(() => {
    getCashPoolBatches({ cashPoolId })
      .then((batches) => {
        const batchesWithoutUnpooled = batches.filter(({ status }) => status !== batchStatus.UNPOOLED);
        setBatchFilter(batchesWithoutUnpooled[batchesWithoutUnpooled.length - 1]);
        setBatches(batches);
      })
      .catch(errorHandler);
  }, [cashPoolId, setBatchFilter]);

  /**
   * Sets default date range for structural positions to include all batches.
   */
  useEffect(() => {
    if (!batches?.length) return;
    dispatch(
      updateStructuralPositionsDateRange({
        startDate: batches[batches.length - 1].startDate.replace(/-/g, '/'),
        endDate: batches[0].endDate.replace(/-/g, '/'),
      })
    );
  }, [dispatch, batches]);

  const onDeleteClick = async () => {
    const { cashPoolId, batchId } = isDeleteModalShowing;
    try {
      await deleteCashPoolBatch({ cashPoolId, batchId });
      const [cashPool] = await Promise.all([
        getCashPool({ cashPoolId }),
        getCashPoolBatches({ cashPoolId }).then(setBatches),
      ]);
      dispatch(setCashPool(cashPool));
      dispatch(updateCashPoolTrigger());
      showToast('Batch successfully deleted.');
    } catch (err) {
      errorHandler(err);
    } finally {
      setIsDeleteModalShowing(false);
    }
  };

  const setIsPooling = (batchId, isPooling) => {
    const updatedBatches = batches.map((batch) => (batch.id === batchId ? { ...batch, isPooling } : batch));
    setBatches(updatedBatches);
  };

  if (!batches) return <LoadingSpinner />;

  return (
    <>
      <Table
        actionColumn={(item) =>
          renderTableActionColumn({
            item,
            firstBatchId: batches[0].id,
            batches,
            setBatches,
            setIsDeleteModalShowing,
            history,
            dispatch,
            setIsPooling,
          })
        }
        columns={columns}
        data={getCashPoolBatchesData(batches, userInfo)}
        isSearchable
        customPageSize={limit}
        customPageIndex={paginationOffset}
        isPaginationHidden
        rightTitleContent={<BatchesCreateButton setBatches={setBatches} />}
        onItemClick={(item) => setBatchFilter(item, true)}
      />
      <Pagination
        canNextPage={batches.length - paginationOffset * limit > paginationOffset * limit}
        canPreviousPage={paginationOffset !== 0}
        pageCount={batches.length / limit}
        forcePage={paginationOffset}
        onPageChange={onPageChange}
        isShowing={batches.length > limit}
      />
      <DeleteModal
        isShowing={isDeleteModalShowing}
        item="import"
        additionalInfo="Deletion will also remove all interest entries in the Interest section."
        handleOnDeleteClick={onDeleteClick}
        handleOnHide={() => setIsDeleteModalShowing(false)}
      />
    </>
  );
};

export default BatchesTable;
